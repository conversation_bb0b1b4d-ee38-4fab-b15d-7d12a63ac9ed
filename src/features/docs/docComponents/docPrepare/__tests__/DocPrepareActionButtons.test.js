import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { createStore } from 'redux';
import DocPrepareActionButtons from '../DocPrepareActionButtons';

// Mock the firestore service
jest.mock('../../../../../app/firestore/firestoreService', () => ({
  updateDocInDb: jest.fn(() => Promise.resolve()),
  getStateFormByTitleFromDb: jest.fn(() => Promise.resolve(null)),
  updateStateFormInDb: jest.fn(() => Promise.resolve()),
}));

// Mock the firebase service
jest.mock('../../../../../app/firestore/firebaseService', () => ({
  uploadBlobToStorage: jest.fn(() => Promise.resolve()),
}));

// Mock the pdfLib
jest.mock('../../../../../app/pdfLib/pdfLib', () => ({
  addAnnotsAndFlattenPdf: jest.fn(() => Promise.resolve(new ArrayBuffer(8))),
}));

// Mock react-responsive
jest.mock('react-responsive', () => ({
  useMediaQuery: jest.fn(() => false),
}));

// Mock react-toastify
jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Create a mock store
const createMockStore = (initialState) => {
  const mockReducer = (state = initialState) => state;
  return createStore(mockReducer);
};

const mockState = {
  annot: {
    annots: [],
    signerListDisplay: [],
    selectedSigner: null,
    pageScalePrepare: 1,
  },
  doc: {
    doc: {
      id: 'test-doc-id',
      transactionId: 'test-transaction-id',
      isFillableByClient: true,
      sharingWithRole: {},
    },
  },
  transaction: {
    transaction: {
      id: 'test-transaction-id',
    },
    allParties: [
      {
        id: 'buyer-1',
        role: 'Buyer',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      },
      {
        id: 'seller-1',
        role: 'Seller',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
      },
    ],
  },
  profile: {
    currentUserProfile: {
      id: 'agent-1',
      firstName: 'Agent',
      lastName: 'Smith',
    },
  },
  modals: {
    modalType: '',
  },
};

const renderWithProviders = (component, state = mockState) => {
  const store = createMockStore(state);
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('DocPrepareActionButtons', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders send for signing button', () => {
    renderWithProviders(<DocPrepareActionButtons />);
    
    const sendButton = screen.getByText(/Send for Signing/i);
    expect(sendButton).toBeInTheDocument();
  });

  test('shows confirmation modal when doc is fillable by client and not shared', () => {
    const store = createMockStore(mockState);
    const dispatchSpy = jest.spyOn(store, 'dispatch');
    
    render(
      <Provider store={store}>
        <BrowserRouter>
          <DocPrepareActionButtons />
        </BrowserRouter>
      </Provider>
    );
    
    const sendButton = screen.getByText(/Send for Signing/i);
    fireEvent.click(sendButton);
    
    // Check that the confirmation modal is opened
    expect(dispatchSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'modals/openModal',
        payload: expect.objectContaining({
          modalType: 'DocFillableByClientConfirm',
        }),
      })
    );
  });

  test('proceeds directly to SendForSigning when doc is not fillable by client', () => {
    const stateWithNonFillableDoc = {
      ...mockState,
      doc: {
        doc: {
          ...mockState.doc.doc,
          isFillableByClient: false,
        },
      },
    };
    
    const store = createMockStore(stateWithNonFillableDoc);
    const dispatchSpy = jest.spyOn(store, 'dispatch');
    
    render(
      <Provider store={store}>
        <BrowserRouter>
          <DocPrepareActionButtons />
        </BrowserRouter>
      </Provider>
    );
    
    const sendButton = screen.getByText(/Send for Signing/i);
    fireEvent.click(sendButton);
    
    // Should proceed directly to SendForSigning modal
    expect(dispatchSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'modals/openModal',
        payload: expect.objectContaining({
          modalType: 'SendForSigning',
        }),
      })
    );
  });

  test('proceeds directly to SendForSigning when doc has been shared with clients', () => {
    const stateWithSharedDoc = {
      ...mockState,
      doc: {
        doc: {
          ...mockState.doc.doc,
          isFillableByClient: true,
          sharingWithRole: {
            buyer: '<EMAIL>', // Document has been shared with buyer
          },
        },
      },
    };
    
    const store = createMockStore(stateWithSharedDoc);
    const dispatchSpy = jest.spyOn(store, 'dispatch');
    
    render(
      <Provider store={store}>
        <BrowserRouter>
          <DocPrepareActionButtons />
        </BrowserRouter>
      </Provider>
    );
    
    const sendButton = screen.getByText(/Send for Signing/i);
    fireEvent.click(sendButton);
    
    // Should proceed directly to SendForSigning modal since doc has been shared
    expect(dispatchSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'modals/openModal',
        payload: expect.objectContaining({
          modalType: 'SendForSigning',
        }),
      })
    );
  });
});
